bl_info = {
    "name": "Arcane SDF",
    "author": "Your Name",
    "version": (1, 0, 0),
    "blender": (4, 2, 0),
    "location": "View3D > Sidebar > Arcane SDF",
    "description": "A modern SDF modeling toolkit for Blender",
    "warning": "",
    "doc_url": "",
    "category": "3D View",
}

import bpy

# Import local modules
from . import properties, shaders, utils, tree_system, tree_operators, tree_panels, matcap_operators

# List of modules to register/unregister
MODULES = [
    tree_system,      # Register tree system first
    properties,       # Then properties (which uses tree_system)
    tree_operators,
    tree_panels,
    matcap_operators, # Register matcap operators
    shaders,
    utils,
]


def register():
    """Register all addon classes"""
    # First unregister to ensure clean state
    unregister()

    for module in MODULES:
        if hasattr(module, 'register'):
            try:
                module.register()
            except Exception as e:
                print(f"Error registering module {module.__name__}: {e}")
                # Continue with other modules
                continue
    print(f"Registered {bl_info['name']}")


def unregister():
    """Unregister all addon classes"""
    for module in reversed(MODULES):
        if hasattr(module, 'unregister'):
            try:
                module.unregister()
            except Exception as e:
                print(f"Error unregistering module {module.__name__}: {e}")
                # Continue with other modules
                continue
    print(f"Unregistered {bl_info['name']}")


if __name__ == "__main__":
    register()
