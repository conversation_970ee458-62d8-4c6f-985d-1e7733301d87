import bpy
import gpu
from gpu_extras.batch import batch_for_shader
from mathutils import Matrix

# Vertex shader for fullscreen quad
vertex_shader = """
    in vec2 position;
    out vec2 texCoord;

    void main()
    {
        gl_Position = vec4(position, 0.0, 1.0);
        texCoord = position * 0.5 + 0.5;
    }
"""

# Fragment shader template - the sceneSDF function will be injected
fragment_shader_template = """
    uniform vec2 resolution;
    uniform mat4 ViewMatrixInverse;
    uniform vec3 cameraPos;
    uniform float max_steps;
    uniform float max_dist;
    uniform float surface_threshold;

    // Matcap uniforms
    uniform sampler2D matcap_texture;
    uniform bool use_matcap;
    uniform float matcap_intensity;
    uniform int shading_mode; // 0=diffuse, 1=matcap, 2=mixed
    uniform vec3 base_color;

    in vec2 texCoord;
    out vec4 fragColor;
    
    // SDF Primitives
    float sdSphere(vec3 p, float r) {
        return length(p) - r;
    }
    
    float sdBox(vec3 p, vec3 b) {
        vec3 q = abs(p) - b;
        return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
    }
    
    float sdCylinder(vec3 p, float r, float h) {
        vec2 d = abs(vec2(length(p.xz), p.y)) - vec2(r, h);
        return min(max(d.x, d.y), 0.0) + length(max(d, 0.0));
    }
    
    float sdTorus(vec3 p, float R, float r) {
        vec2 q = vec2(length(p.xz) - R, p.y);
        return length(q) - r;
    }

    // Edge modification functions
    float opBevel(float d, float r) {
        return d - r;
    }

    float opChamfer(float d, float c) {
        return max(d, abs(d) - c);
    }

    // Enhanced primitives with bevel/chamfer support
    float sdBoxBeveled(vec3 p, vec3 b, float r) {
        vec3 q = abs(p) - b;
        return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - r;
    }

    float sdBoxChamfered(vec3 p, vec3 b, float c) {
        // Proper chamfer: cut the edges at 45-degree angles
        // Start with the normal box distance
        vec3 q = abs(p) - b;
        float boxDist = length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);

        // Create chamfer cuts - these should SUBTRACT from the box, not replace it
        // We want to cut away material near the edges
        float chamferCut = -1000.0; // Start with a very negative distance (inside)

        // Apply chamfer cuts to the 12 edges of the box
        // Each cut removes material where two faces meet
        // X-edges: cut where Y and Z faces meet
        chamferCut = max(chamferCut, -(abs(q.y) + abs(q.z) - c));
        // Y-edges: cut where X and Z faces meet
        chamferCut = max(chamferCut, -(abs(q.x) + abs(q.z) - c));
        // Z-edges: cut where X and Y faces meet
        chamferCut = max(chamferCut, -(abs(q.x) + abs(q.y) - c));

        // Apply the chamfer cuts to the box (subtract the cuts from the box)
        return max(boxDist, chamferCut);
    }

    float sdCylinderBeveled(vec3 p, float r, float h, float bevel) {
        vec2 d = abs(vec2(length(p.xz), p.y)) - vec2(r, h);
        return min(max(d.x, d.y), 0.0) + length(max(d, 0.0)) - bevel;
    }

    float sdCylinderChamfered(vec3 p, float r, float h, float chamfer) {
        // Proper chamfer: cut the edges at 45-degree angles
        // Start with the normal cylinder distance
        vec2 d = abs(vec2(length(p.xz), p.y)) - vec2(r, h);
        float cylDist = min(max(d.x, d.y), 0.0) + length(max(d, 0.0));

        // Create chamfer cuts on the circular edges (top and bottom)
        float edgeDist = abs(d.x) + abs(d.y) - chamfer;

        // The final distance is the intersection of the cylinder with the chamfer cuts
        return max(cylDist, edgeDist);
    }

    // Fillet functions - for rounding internal corners
    float sdBoxFilleted(vec3 p, vec3 b, float f) {
        // Box with filleted (rounded internal) corners
        vec3 q = abs(p) - b + vec3(f);
        return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0) - f;
    }

    float sdCylinderFilleted(vec3 p, float r, float h, float f) {
        // Cylinder with filleted internal edges
        vec2 d = abs(vec2(length(p.xz), p.y)) - vec2(r - f, h - f);
        return min(max(d.x, d.y), 0.0) + length(max(d, 0.0)) - f;
    }
    
    // SDF Operations
    float opUnion(float d1, float d2) {
        return min(d1, d2);
    }

    float opSubtraction(float d1, float d2) {
        return max(-d1, d2);
    }

    float opIntersection(float d1, float d2) {
        return max(d1, d2);
    }

    // Smooth operations
    float smin(float a, float b, float k) {
        float h = clamp(0.5 + 0.5 * (b - a) / k, 0.0, 1.0);
        return mix(b, a, h) - k * h * (1.0 - h);
    }

    float smax(float a, float b, float k) {
        return -smin(-a, -b, k);
    }

    // Smooth subtraction - specialized function
    float ssubtract(float a, float b, float k) {
        return smax(a, -b, k);
    }
    
    // Scene SDF - This will be generated by the node system
    float sceneSDF(vec3 p) {
        {scene_sdf_code}
    }
    
    // Raymarching function
    float raymarch(vec3 ro, vec3 rd, float max_dist, float max_steps, float surface_threshold) {
        float depth = 0.0;
        
        for (int i = 0; i < int(max_steps); i++) {
            vec3 p = ro + depth * rd;
            float dist = sceneSDF(p);
            
            if (dist < surface_threshold) {
                return depth;
            }
            
            depth += dist;
            
            if (depth >= max_dist) {
                return -1.0;
            }
        }
        
        return -1.0;
    }
    
    // Calculate normal using central differences
    vec3 calculateNormal(vec3 p) {
        const float h = 0.0001;
        const vec2 k = vec2(1.0, -1.0) * h;

        return normalize(k.xyy * sceneSDF(p + k.xyy) +
                        k.yyx * sceneSDF(p + k.yyx) +
                        k.yxy * sceneSDF(p + k.yxy) +
                        k.xxx * sceneSDF(p + k.xxx));
    }

    // Convert world normal to matcap UV coordinates
    vec2 matcapUV(vec3 normal, mat4 viewMatrix) {
        // Transform normal to view space
        vec3 viewNormal = normalize((viewMatrix * vec4(normal, 0.0)).xyz);

        // Convert to matcap UV (sphere mapping)
        vec2 uv = viewNormal.xy * 0.5 + 0.5;
        return uv;
    }

    // Sample matcap texture
    vec3 sampleMatcap(vec3 normal, mat4 viewMatrix) {
        vec2 uv = matcapUV(normal, viewMatrix);
        return texture(matcap_texture, uv).rgb;
    }

    // Calculate diffuse lighting
    vec3 calculateDiffuse(vec3 normal) {
        vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
        float diff = max(dot(normal, lightDir), 0.1);
        return vec3(diff);
    }
    
    // Main shader function
    void main() {
        // Calculate ray direction
        vec2 uv = (2.0 * texCoord - 1.0) * vec2(resolution.x / resolution.y, 1.0);

        // Create ray from camera
        vec3 ro = cameraPos;
        vec4 rd_view = vec4(uv, -1.0, 0.0);
        vec3 rd = normalize((ViewMatrixInverse * rd_view).xyz);

        // Raymarch the scene
        float t = raymarch(ro, rd, max_dist, max_steps, surface_threshold);

        // Shade based on hit distance
        if (t > 0.0) {
            // Calculate hit position and normal
            vec3 hitPos = ro + t * rd;
            vec3 normal = calculateNormal(hitPos);

            vec3 finalColor = base_color;

            // Apply shading based on mode
            if (shading_mode == 0) {
                // Diffuse only
                vec3 diffuse = calculateDiffuse(normal);
                finalColor *= diffuse;
            }
            else if (shading_mode == 1 && use_matcap) {
                // Matcap only
                vec3 matcapColor = sampleMatcap(normal, ViewMatrixInverse);
                finalColor = mix(finalColor, matcapColor, matcap_intensity);
            }
            else if (shading_mode == 2 && use_matcap) {
                // Mixed: diffuse + matcap
                vec3 diffuse = calculateDiffuse(normal);
                vec3 matcapColor = sampleMatcap(normal, ViewMatrixInverse);
                vec3 mixedColor = mix(diffuse, matcapColor, matcap_intensity);
                finalColor *= mixedColor;
            }
            else {
                // Fallback to diffuse
                vec3 diffuse = calculateDiffuse(normal);
                finalColor *= diffuse;
            }

            // Set fragment color
            fragColor = vec4(finalColor, 1.0);
        } else {
            // Background color (sky blue)
            fragColor = vec4(0.5, 0.7, 1.0, 1.0);
        }
    }
"""

def _draw_callback_helper():
    """Wrapper to call the renderer's draw callback"""
    try:
        context = bpy.context
        if context:
            SDFRenderer._draw_callback(context)
    except Exception as e:
        print(f"SDF Renderer: Error in draw callback helper: {e}")
        # Disable renderer on persistent errors to prevent crashes
        SDFRenderer.disable()


class SDFRenderer:
    """Handles SDF viewport rendering"""
    _handle = None
    _shader = None
    _batch = None
    _enabled = False
    _glsl_cache = None
    _timer_handle = None
    _matcap_texture = None
    _current_settings = {
        'max_steps': 100,
        'max_dist': 100.0,
        'surface_threshold': 0.001,
    }
    
    @classmethod
    def is_enabled(cls):
        """Check if the renderer is enabled"""
        return cls._enabled
    
    @classmethod
    def _is_handler_valid(cls):
        """Check if the draw handler is valid"""
        return cls._handle is not None and cls._enabled
    
    @classmethod
    def update_settings(cls, **kwargs):
        """Update renderer settings

        Args:
            **kwargs: Settings to update (max_steps, max_dist, surface_threshold)
        """
        cls._current_settings.update(kwargs)

        # Recreate shader if needed
        if cls._shader is not None:
            cls._create_shader_program()

    @classmethod
    def load_matcap_texture(cls, image_path):
        """Load a matcap texture from file

        Args:
            image_path: Path to the matcap image file
        """
        try:
            import bpy

            # Load image in Blender
            if image_path in bpy.data.images:
                image = bpy.data.images[image_path]
            else:
                image = bpy.data.images.load(image_path)

            # Ensure image is loaded
            if image.size[0] == 0 or image.size[1] == 0:
                image.reload()

            # Create GPU texture
            cls._matcap_texture = gpu.texture.from_image(image)
            print(f"SDF Renderer: Loaded matcap texture: {image_path}")
            return True

        except Exception as e:
            print(f"SDF Renderer: Failed to load matcap texture {image_path}: {e}")
            cls._matcap_texture = None
            return False

    @classmethod
    def load_matcap_from_blender_image(cls, image_name):
        """Load a matcap texture from a Blender image

        Args:
            image_name: Name of the image in bpy.data.images
        """
        try:
            import bpy

            if image_name not in bpy.data.images:
                print(f"SDF Renderer: Image '{image_name}' not found in Blender")
                return False

            image = bpy.data.images[image_name]

            # Ensure image is loaded
            if image.size[0] == 0 or image.size[1] == 0:
                image.reload()

            # Create GPU texture
            cls._matcap_texture = gpu.texture.from_image(image)
            print(f"SDF Renderer: Loaded matcap texture from Blender image: {image_name}")
            return True

        except Exception as e:
            print(f"SDF Renderer: Failed to load matcap from Blender image {image_name}: {e}")
            cls._matcap_texture = None
            return False

    @classmethod
    def create_default_matcap(cls):
        """Create a simple default matcap texture"""
        try:
            import bpy
            import math

            # Create a simple spherical matcap
            size = 256
            pixels = []

            center = size // 2
            for y in range(size):
                for x in range(size):
                    # Calculate distance from center
                    dx = (x - center) / center
                    dy = (y - center) / center
                    dist = math.sqrt(dx*dx + dy*dy)

                    if dist <= 1.0:
                        # Simple spherical shading
                        z = math.sqrt(max(0, 1.0 - dist*dist))

                        # Simple lighting calculation
                        light_x, light_y, light_z = 0.5, 0.5, 0.7
                        light_len = math.sqrt(light_x*light_x + light_y*light_y + light_z*light_z)
                        light_x /= light_len
                        light_y /= light_len
                        light_z /= light_len

                        # Normalize normal
                        normal_len = math.sqrt(dx*dx + dy*dy + z*z)
                        nx = dx / normal_len
                        ny = dy / normal_len
                        nz = z / normal_len

                        # Diffuse lighting
                        diffuse = max(0.1, nx*light_x + ny*light_y + nz*light_z)

                        # Set color (RGBA)
                        pixels.extend([diffuse * 0.8, diffuse * 0.8, diffuse * 0.9, 1.0])
                    else:
                        # Outside sphere - transparent
                        pixels.extend([0.5, 0.5, 0.6, 0.0])

            # Create Blender image
            image_name = "SDF_Default_Matcap"
            if image_name in bpy.data.images:
                bpy.data.images.remove(bpy.data.images[image_name])

            image = bpy.data.images.new(image_name, size, size, alpha=True)
            image.pixels = pixels
            image.pack()

            # Create GPU texture
            cls._matcap_texture = gpu.texture.from_image(image)
            print("SDF Renderer: Created default matcap texture")
            return True

        except Exception as e:
            print(f"SDF Renderer: Failed to create default matcap: {e}")
            import traceback
            traceback.print_exc()
            return False

    @classmethod
    def _set_matcap_uniforms(cls, scene):
        """Set matcap-related uniforms"""
        try:
            # Get material properties
            material_props = None
            if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
                material_props = scene.sdf.material

            # Default values
            use_matcap = False
            matcap_intensity = 1.0
            shading_mode = 0  # diffuse
            base_color = (0.8, 0.8, 0.8)

            # Get values from properties if available
            if material_props:
                use_matcap = getattr(material_props, 'use_matcap', False)
                matcap_intensity = getattr(material_props, 'matcap_intensity', 1.0)
                shading_mode_str = getattr(material_props, 'shading_mode', '0')
                shading_mode = int(shading_mode_str)  # Convert enum string to int
                base_color = getattr(material_props, 'base_color', (0.8, 0.8, 0.8, 1.0))[:3]

            # Set uniforms
            cls._shader.uniform_bool("use_matcap", use_matcap)
            cls._shader.uniform_float("matcap_intensity", matcap_intensity)
            cls._shader.uniform_int("shading_mode", shading_mode)
            cls._shader.uniform_float("base_color", base_color)

            # Bind matcap texture if available and enabled
            if use_matcap and cls._matcap_texture:
                gpu.state.active_texture_set(cls._matcap_texture, 0)
                cls._shader.uniform_sampler("matcap_texture", 0)
            elif use_matcap and not cls._matcap_texture:
                # Try to create default matcap if none exists
                cls.create_default_matcap()
                if cls._matcap_texture:
                    gpu.state.active_texture_set(cls._matcap_texture, 0)
                    cls._shader.uniform_sampler("matcap_texture", 0)

        except Exception as e:
            print(f"SDF Renderer: Error setting matcap uniforms: {e}")
            # Set safe defaults
            try:
                cls._shader.uniform_bool("use_matcap", False)
                cls._shader.uniform_float("matcap_intensity", 1.0)
                cls._shader.uniform_int("shading_mode", 0)
                cls._shader.uniform_float("base_color", (0.8, 0.8, 0.8))
            except:
                pass
    
    @classmethod
    def enable(cls):
        """Enable the SDF renderer"""
        if cls._enabled:
            return

        try:
            # Create shader if it doesn't exist
            if cls._shader is None:
                if not cls._create_shader_program():
                    raise RuntimeError("Failed to create shader program")

            # Create batch if it doesn't exist
            if cls._batch is None and cls._shader is not None:
                vertices = [
                    (-1, -1), (1, -1), (1, 1),  # First triangle
                    (-1, -1), (1, 1), (-1, 1)   # Second triangle
                ]
                cls._batch = batch_for_shader(
                    cls._shader, 'TRIS',
                    {"position": vertices}
                )

            # Add draw handler
            if cls._handle is None:
                cls._handle = bpy.types.SpaceView3D.draw_handler_add(
                    _draw_callback_helper, (), 'WINDOW', 'POST_VIEW'
                )

            # Add timer for periodic updates
            if cls._timer_handle is None:
                cls._timer_handle = bpy.app.timers.register(cls._timer_update, first_interval=0.1, persistent=True)

            cls._enabled = True
            print("SDF Renderer enabled")
        except Exception as e:
            print(f"SDF Renderer: Error enabling renderer: {e}")
            cls.disable() # Ensure clean state on failure
    
    @classmethod
    def disable(cls):
        if not cls._enabled:
            return
        cls._enabled = False

        # Remove draw handler
        if cls._handle:
            try:
                bpy.types.SpaceView3D.draw_handler_remove(cls._handle, 'WINDOW')
            except (ValueError, RuntimeError) as e:
                print(f"SDF Renderer: Error removing draw handler: {e}")
            finally:
                cls._handle = None

        # Remove timer
        if cls._timer_handle:
            try:
                bpy.app.timers.unregister(cls._timer_handle)
            except (ValueError, RuntimeError) as e:
                print(f"SDF Renderer: Error removing timer: {e}")
            finally:
                cls._timer_handle = None
    
    @classmethod
    def refresh_shader(cls):
        """Refresh the shader with the current tree system"""
        print("SDF Renderer: Refreshing shader for tree system")
        cls._create_shader_program()

        # Force viewport redraw
        cls._force_viewport_redraw()

    @classmethod
    def _force_viewport_redraw(cls):
        """Force all 3D viewports to redraw"""
        try:
            import bpy
            # Redraw all areas
            for window in bpy.context.window_manager.windows:
                for area in window.screen.areas:
                    if area.type == 'VIEW_3D':
                        area.tag_redraw()

            # Also trigger a general redraw
            if bpy.context.window_manager:
                bpy.context.window_manager.tag_redraw()

        except Exception as e:
            print(f"SDF Renderer: Error forcing viewport redraw: {e}")

    @classmethod
    def _timer_update(cls):
        """Timer callback for periodic viewport updates"""
        try:
            if cls._enabled and cls._shader:
                # Only redraw if we have a valid context and tree items
                if bpy.context.scene and hasattr(bpy.context.scene, 'sdf_tree'):
                    tree = bpy.context.scene.sdf_tree
                    if tree.items:
                        cls._force_viewport_redraw()

            # Return the interval for next update (0.1 seconds)
            return 0.1

        except Exception as e:
            print(f"SDF Renderer: Error in timer update: {e}")
            # Return None to stop the timer on error
            return None
    
    @classmethod
    def reset(cls):
        """Reset the renderer state"""
        cls.disable()
        cls._shader = None
        cls._batch = None
        cls._enabled = False
        cls._handle = None
        cls._timer_handle = None
        print("SDF Renderer reset")
    
    @classmethod
    def _generate_scene_glsl(cls):
        """Generate GLSL code for the scene SDF from the tree system"""
        try:
            import bpy
            scene = bpy.context.scene
            if hasattr(scene, 'sdf_tree') and scene.sdf_tree.items:
                print("SDF Renderer: Using tree system for GLSL generation")
                glsl_code = scene.sdf_tree.generate_glsl()
                print(f"SDF Renderer: Generated tree GLSL: {glsl_code}")
                return glsl_code
            else:
                print("SDF Renderer: No tree items found, returning empty scene")
                return "return 1000.0;"  # No geometry - empty scene
        except Exception as e:
            print(f"SDF Renderer: Error with tree system: {e}")
            import traceback
            traceback.print_exc()
            return "return 1000.0;"  # Fallback to empty scene
    
    @classmethod
    def _create_shader_program(cls):
        try:
            # Check if GPU module is available
            if not hasattr(gpu, 'types') or not hasattr(gpu.types, 'GPUShader'):
                print("GPU shader support not available")
                return None
            
            # Generate scene GLSL code
            scene_glsl = cls._generate_scene_glsl()
            
            # Create fragment shader with the generated scene code
            fragment_shader = fragment_shader_template.replace("{scene_sdf_code}", scene_glsl)

            print(f"SDF Renderer: Full scene GLSL being used: {scene_glsl}")

            # Create shader
            shader = gpu.types.GPUShader(vertex_shader, fragment_shader)
            cls._shader = shader
            print(f"SDF Renderer: Created shader with scene GLSL: {scene_glsl[:100]}...")
            return shader
        except Exception as e:
            print(f"Shader creation failed: {e}")
            import traceback
            traceback.print_exc()
            return None

    @classmethod
    def register(cls):
        if cls._shader is not None:
            return
            
        try:
            # Create shader program
            if not cls._create_shader_program():
                raise Exception("Failed to create shader program")
            
            # Create fullscreen quad vertices
            vertices = [
                (-1, -1), (1, -1), (1, 1),  # First triangle
                (-1, -1), (1, 1), (-1, 1)   # Second triangle
            ]
            
            cls._batch = batch_for_shader(
                cls._shader, 'TRIS',
                {"position": vertices}
            )
            
            # Initial enable
            cls.enable()
            print("SDF Renderer initialized successfully")
            return True
            
        except Exception as e:
            print(f"Failed to initialize SDF renderer: {e}")
            import traceback
            traceback.print_exc()
            cls.unregister()
            return False
    
    @classmethod
    def unregister(cls):
        if cls._handle is not None:
            try:
                bpy.types.SpaceView3D.draw_handler_remove(cls._handle, 'WINDOW')
            except (ValueError, RuntimeError) as e:
                print(f"SDF Renderer: Error removing draw handler during unregister: {e}")
            finally:
                cls._handle = None
        
        cls._shader = None
        cls._batch = None
    
    @classmethod
    def _draw_callback(cls, context):
        """Draw callback for viewport rendering"""
        if not cls._shader or not cls._batch or not cls._is_handler_valid():
            print("SDF Renderer: Draw callback skipped - missing shader, batch, or invalid handler")
            return
            
        # Check if context is valid
        if not context or not hasattr(context, 'region') or not context.region:
            print("SDF Renderer: Draw callback skipped - invalid context")
            return
            
        # Get active region
        region = context.region
        if not hasattr(context, 'space_data') or not context.space_data:
            print("SDF Renderer: Draw callback skipped - no space_data")
            return
            
        region3d = context.space_data.region_3d
        
        # Check if we have a valid tree system
        scene = context.scene
        if not hasattr(scene, 'sdf_tree') or not scene.sdf_tree.items:
            print("SDF Renderer: Draw callback skipped - no tree items")
            return

        # Check if viewport rendering is enabled
        if not hasattr(scene, 'sdf') or not scene.sdf.sdf_show_in_viewport:
            print("SDF Renderer: Draw callback skipped - viewport rendering disabled")
            return
            
        try:
            print("SDF Renderer: Drawing frame...")

            # Bind shader
            cls._shader.bind()

            # Set shader uniforms
            cls._shader.uniform_float("resolution", (region.width, region.height))
            cls._shader.uniform_float("ViewMatrixInverse", region3d.view_matrix.inverted())
            cls._shader.uniform_float("cameraPos", region3d.view_matrix.inverted().translation)

            # Set rendering parameters from current settings
            cls._shader.uniform_float("max_steps", float(cls._current_settings['max_steps']))
            cls._shader.uniform_float("max_dist", float(cls._current_settings['max_dist']))
            cls._shader.uniform_float("surface_threshold", float(cls._current_settings['surface_threshold']))

            # Set matcap uniforms
            cls._set_matcap_uniforms(scene)

            # Draw fullscreen quad
            cls._batch.draw(cls._shader)
            
            print("SDF Renderer: Frame drawn successfully")
            
        except Exception as e:
            print(f"Error in SDF renderer: {e}")
            import traceback
            traceback.print_exc()
            # Disable renderer on error to prevent further crashes
            cls.disable()
            return
            
        finally:
            # Clean up OpenGL state
            gpu.state.blend_set('NONE')
            gpu.state.depth_test_set('NONE')
            
            # Unbind shader if available
            if hasattr(gpu, 'shader'):
                gpu.shader.unbind()
            elif hasattr(gpu, 'shader_from_builtin'):
                gpu.shader.from_builtin('3D_UNIFORM_COLOR').unbind()

def register():
    try:
        SDFRenderer.reset()  # Ensure clean state
        SDFRenderer.register()
    except Exception as e:
        print(f"Failed to register SDF renderer: {e}")
        import traceback
        traceback.print_exc()

def unregister():
    try:
        SDFRenderer.reset()  # Clean shutdown
    except Exception as e:
        print(f"Failed to unregister SDF renderer: {e}")
