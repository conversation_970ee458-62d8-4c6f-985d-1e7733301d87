"""
Panel background image system for Blender UI panels.
Allows overlaying background images on panels with transparency.
"""

import bpy
import gpu
import blf
from gpu_extras.batch import batch_for_shader
from mathutils import Vector
import os


class PanelBackgroundManager:
    """Manages background images for UI panels"""
    
    _backgrounds = {}  # Panel ID -> background info
    _textures = {}     # Image name -> GPU texture
    _shader = None
    
    @classmethod
    def initialize(cls):
        """Initialize the background system"""
        try:
            # Create shader for drawing background images
            vertex_shader = """
                in vec2 position;
                in vec2 texCoord;
                out vec2 uvCoord;
                
                uniform mat4 ModelViewProjectionMatrix;
                
                void main() {
                    uvCoord = texCoord;
                    gl_Position = ModelViewProjectionMatrix * vec4(position, 0.0, 1.0);
                }
            """
            
            fragment_shader = """
                in vec2 uvCoord;
                out vec4 fragColor;
                
                uniform sampler2D image;
                uniform float alpha;
                uniform vec3 tint_color;
                
                void main() {
                    vec4 tex_color = texture(image, uvCoord);
                    fragColor = vec4(tex_color.rgb * tint_color, tex_color.a * alpha);
                }
            """
            
            cls._shader = gpu.types.GPUShader(vertex_shader, fragment_shader)
            print("Panel Background Manager: Initialized successfully")
            return True
            
        except Exception as e:
            print(f"Panel Background Manager: Failed to initialize: {e}")
            return False
    
    @classmethod
    def set_panel_background(cls, panel_id, image_name, alpha=0.25, tint_color=(1.0, 1.0, 1.0), 
                           offset=(0, 0), scale=(1.0, 1.0), blend_mode='ALPHA'):
        """Set background image for a panel
        
        Args:
            panel_id: Panel identifier (e.g., 'SDF_PT_tree_panel')
            image_name: Name of image in bpy.data.images
            alpha: Transparency (0.0-1.0)
            tint_color: RGB color to tint the image
            offset: (x, y) offset in pixels
            scale: (x, y) scale factors
            blend_mode: 'ALPHA', 'MULTIPLY', 'OVERLAY'
        """
        try:
            if image_name not in bpy.data.images:
                print(f"Panel Background: Image '{image_name}' not found")
                return False
            
            # Load texture if not already loaded
            if image_name not in cls._textures:
                image = bpy.data.images[image_name]
                if image.size[0] == 0 or image.size[1] == 0:
                    image.reload()
                
                cls._textures[image_name] = gpu.texture.from_image(image)
                print(f"Panel Background: Loaded texture for {image_name}")
            
            # Store background info
            cls._backgrounds[panel_id] = {
                'image_name': image_name,
                'alpha': alpha,
                'tint_color': tint_color,
                'offset': offset,
                'scale': scale,
                'blend_mode': blend_mode,
                'enabled': True
            }
            
            print(f"Panel Background: Set background for {panel_id}")
            return True
            
        except Exception as e:
            print(f"Panel Background: Error setting background: {e}")
            return False
    
    @classmethod
    def remove_panel_background(cls, panel_id):
        """Remove background from a panel"""
        if panel_id in cls._backgrounds:
            del cls._backgrounds[panel_id]
            print(f"Panel Background: Removed background for {panel_id}")
    
    @classmethod
    def draw_panel_background(cls, panel_id, context, region):
        """Draw background for a specific panel
        
        Call this from the panel's draw method
        """
        if not cls._shader or panel_id not in cls._backgrounds:
            return
        
        bg_info = cls._backgrounds[panel_id]
        if not bg_info['enabled']:
            return
        
        try:
            # Get the texture
            texture = cls._textures.get(bg_info['image_name'])
            if not texture:
                return
            
            # Calculate panel bounds (approximate)
            # Note: Getting exact panel bounds is tricky in Blender
            # This is a simplified approach
            region_width = region.width
            region_height = region.height
            
            # Create quad vertices for the background
            offset_x, offset_y = bg_info['offset']
            scale_x, scale_y = bg_info['scale']
            
            # Simple full-region background (can be refined)
            vertices = [
                (offset_x, offset_y),
                (region_width * scale_x + offset_x, offset_y),
                (region_width * scale_x + offset_x, region_height * scale_y + offset_y),
                (offset_x, region_height * scale_y + offset_y)
            ]
            
            tex_coords = [
                (0.0, 0.0),
                (1.0, 0.0),
                (1.0, 1.0),
                (0.0, 1.0)
            ]
            
            indices = [(0, 1, 2), (0, 2, 3)]
            
            # Create batch
            batch = batch_for_shader(
                cls._shader, 'TRIS',
                {
                    "position": vertices,
                    "texCoord": tex_coords
                },
                indices=indices
            )
            
            # Set up GPU state
            gpu.state.blend_set('ALPHA')
            gpu.state.depth_test_set('NONE')
            
            # Bind shader and set uniforms
            cls._shader.bind()
            
            # Bind texture
            try:
                texture.bind(0)
                cls._shader.uniform_sampler("image", 0)
            except AttributeError:
                # Fallback for older Blender versions
                gpu.state.active_texture_set(texture, 0)
                cls._shader.uniform_sampler("image", 0)
            
            cls._shader.uniform_float("alpha", bg_info['alpha'])
            cls._shader.uniform_float("tint_color", bg_info['tint_color'])
            
            # Draw the background
            batch.draw(cls._shader)
            
            # Restore GPU state
            gpu.state.blend_set('NONE')
            
        except Exception as e:
            print(f"Panel Background: Error drawing background for {panel_id}: {e}")
    
    @classmethod
    def cleanup(cls):
        """Clean up resources"""
        cls._backgrounds.clear()
        cls._textures.clear()
        cls._shader = None


class PanelBackgroundMixin:
    """Mixin class to add background support to panels"""
    
    def draw_background(self, context):
        """Draw background for this panel"""
        try:
            region = context.region
            PanelBackgroundManager.draw_panel_background(self.bl_idname, context, region)
        except Exception as e:
            print(f"Panel Background Mixin: Error drawing background: {e}")


# Properties for managing panel backgrounds
class PanelBackgroundProperties(bpy.types.PropertyGroup):
    """Properties for panel background configuration"""
    
    def _update_background(self, context):
        """Update background when properties change"""
        if self.enabled and self.image_name:
            PanelBackgroundManager.set_panel_background(
                self.panel_id,
                self.image_name,
                self.alpha,
                self.tint_color[:3],
                (self.offset_x, self.offset_y),
                (self.scale_x, self.scale_y),
                self.blend_mode
            )
        else:
            PanelBackgroundManager.remove_panel_background(self.panel_id)
    
    panel_id: bpy.props.StringProperty(
        name="Panel ID",
        description="ID of the panel to apply background to",
        default=""
    )
    
    enabled: bpy.props.BoolProperty(
        name="Enable Background",
        description="Enable background image for this panel",
        default=False,
        update=_update_background
    )
    
    image_name: bpy.props.StringProperty(
        name="Background Image",
        description="Name of the background image",
        default="",
        update=_update_background
    )
    
    alpha: bpy.props.FloatProperty(
        name="Transparency",
        description="Background image transparency",
        default=0.25,
        min=0.0,
        max=1.0,
        update=_update_background
    )
    
    tint_color: bpy.props.FloatVectorProperty(
        name="Tint Color",
        description="Color to tint the background image",
        subtype='COLOR',
        default=(1.0, 1.0, 1.0, 1.0),
        min=0.0,
        max=1.0,
        size=4,
        update=_update_background
    )
    
    offset_x: bpy.props.IntProperty(
        name="Offset X",
        description="Horizontal offset in pixels",
        default=0,
        update=_update_background
    )
    
    offset_y: bpy.props.IntProperty(
        name="Offset Y",
        description="Vertical offset in pixels",
        default=0,
        update=_update_background
    )
    
    scale_x: bpy.props.FloatProperty(
        name="Scale X",
        description="Horizontal scale factor",
        default=1.0,
        min=0.1,
        max=5.0,
        update=_update_background
    )
    
    scale_y: bpy.props.FloatProperty(
        name="Scale Y",
        description="Vertical scale factor",
        default=1.0,
        min=0.1,
        max=5.0,
        update=_update_background
    )
    
    blend_mode: bpy.props.EnumProperty(
        name="Blend Mode",
        description="How to blend the background with the panel",
        items=[
            ('ALPHA', 'Alpha', 'Standard alpha blending'),
            ('MULTIPLY', 'Multiply', 'Multiply blend mode'),
            ('OVERLAY', 'Overlay', 'Overlay blend mode'),
        ],
        default='ALPHA',
        update=_update_background
    )


# Operator to set up panel backgrounds
class SDF_OT_SetPanelBackground(bpy.types.Operator):
    """Set background image for a panel"""
    bl_idname = "sdf.set_panel_background"
    bl_label = "Set Panel Background"
    bl_description = "Set a background image for the SDF panels"
    bl_options = {'REGISTER', 'UNDO'}
    
    panel_id: bpy.props.EnumProperty(
        name="Panel",
        description="Panel to set background for",
        items=[
            ('SDF_PT_tree_panel', 'Main Tree Panel', 'Main SDF Tree Builder panel'),
            ('SDF_PT_tree_settings', 'Settings Panel', 'Render Settings panel'),
        ],
        default='SDF_PT_tree_panel'
    )
    
    image_name: bpy.props.StringProperty(
        name="Image",
        description="Name of the background image",
        default=""
    )
    
    alpha: bpy.props.FloatProperty(
        name="Transparency",
        description="Background transparency",
        default=0.25,
        min=0.0,
        max=1.0
    )
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop(self, "panel_id")
        layout.prop_search(self, "image_name", bpy.data, "images")
        layout.prop(self, "alpha", slider=True)
    
    def execute(self, context):
        try:
            if not self.image_name:
                self.report({'ERROR'}, "No image selected")
                return {'CANCELLED'}
            
            if PanelBackgroundManager.set_panel_background(
                self.panel_id, 
                self.image_name, 
                self.alpha
            ):
                self.report({'INFO'}, f"Background set for {self.panel_id}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to set background")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error: {e}")
            return {'CANCELLED'}


# Registration
classes = (
    PanelBackgroundProperties,
    SDF_OT_SetPanelBackground,
)


def register():
    """Register panel background system"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Initialize the background manager
    PanelBackgroundManager.initialize()
    
    # Add properties to scene
    bpy.types.Scene.panel_backgrounds = bpy.props.CollectionProperty(
        type=PanelBackgroundProperties
    )


def unregister():
    """Unregister panel background system"""
    PanelBackgroundManager.cleanup()
    
    if hasattr(bpy.types.Scene, 'panel_backgrounds'):
        del bpy.types.Scene.panel_backgrounds
    
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except ValueError:
            pass
