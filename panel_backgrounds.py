"""
Simple panel background system for SDF addon.
Automatically applies arcane.png as background to SDF panels.
"""

import bpy
import gpu
from gpu_extras.batch import batch_for_shader
import os


class SDFPanelBackground:
    """Simple background system for SDF panels using arcane.png"""

    _texture = None
    _shader = None
    _initialized = False

    @classmethod
    def initialize(cls):
        """Initialize the background system"""
        if cls._initialized:
            return True

        try:
            # Create simple shader for drawing background
            vertex_shader = """
                in vec2 position;
                in vec2 texCoord;
                out vec2 uvCoord;

                uniform mat4 ModelViewProjectionMatrix;

                void main() {
                    uvCoord = texCoord;
                    gl_Position = ModelViewProjectionMatrix * vec4(position, 0.0, 1.0);
                }
            """

            fragment_shader = """
                in vec2 uvCoord;
                out vec4 fragColor;

                uniform sampler2D arcane_texture;

                void main() {
                    vec4 tex_color = texture(arcane_texture, uvCoord);
                    fragColor = vec4(tex_color.rgb, tex_color.a * 0.25);  // Fixed 25% transparency
                }
            """

            cls._shader = gpu.types.GPUShader(vertex_shader, fragment_shader)
            cls._load_arcane_texture()
            cls._initialized = True
            print("SDF Panel Background: Initialized with arcane.png")
            return True

        except Exception as e:
            print(f"SDF Panel Background: Failed to initialize: {e}")
            return False

    @classmethod
    def _load_arcane_texture(cls):
        """Load arcane.png texture"""
        try:
            # Look for arcane.png in Blender's loaded images first
            if "arcane.png" in bpy.data.images:
                image = bpy.data.images["arcane.png"]
                print("SDF Panel Background: Found arcane.png in Blender images")
            else:
                # Try to load from addon directory
                addon_dir = os.path.dirname(__file__)
                arcane_path = os.path.join(addon_dir, "arcane.png")

                if os.path.exists(arcane_path):
                    image = bpy.data.images.load(arcane_path)
                    image.name = "arcane.png"
                    print(f"SDF Panel Background: Loaded arcane.png from {arcane_path}")
                else:
                    print(f"SDF Panel Background: arcane.png not found at {arcane_path}")
                    return False

            # Ensure image is loaded
            if image.size[0] == 0 or image.size[1] == 0:
                image.reload()

            # Create GPU texture
            cls._texture = gpu.texture.from_image(image)
            return True

        except Exception as e:
            print(f"SDF Panel Background: Error loading arcane.png: {e}")
            return False
    
    @classmethod
    def draw_background(cls, context, region):
        """Draw arcane.png background"""
        if not cls._initialized or not cls._shader or not cls._texture:
            return

        try:
            # Calculate region bounds
            region_width = region.width
            region_height = region.height

            # Create quad vertices for full region background
            vertices = [
                (0, 0),
                (region_width, 0),
                (region_width, region_height),
                (0, region_height)
            ]

            tex_coords = [
                (0.0, 0.0),
                (1.0, 0.0),
                (1.0, 1.0),
                (0.0, 1.0)
            ]

            indices = [(0, 1, 2), (0, 2, 3)]

            # Create batch
            batch = batch_for_shader(
                cls._shader, 'TRIS',
                {
                    "position": vertices,
                    "texCoord": tex_coords
                },
                indices=indices
            )

            # Set up GPU state for transparency
            gpu.state.blend_set('ALPHA')
            gpu.state.depth_test_set('NONE')

            # Bind shader
            cls._shader.bind()

            # Bind arcane texture
            try:
                cls._texture.bind(0)
                cls._shader.uniform_sampler("arcane_texture", 0)
            except AttributeError:
                # Fallback for older Blender versions
                gpu.state.active_texture_set(cls._texture, 0)
                cls._shader.uniform_sampler("arcane_texture", 0)

            # Draw the background
            batch.draw(cls._shader)

            # Restore GPU state
            gpu.state.blend_set('NONE')

        except Exception as e:
            print(f"SDF Panel Background: Error drawing background: {e}")

    @classmethod
    def cleanup(cls):
        """Clean up resources"""
        cls._texture = None
        cls._shader = None
        cls._initialized = False


class SDFPanelMixin:
    """Mixin class to add arcane.png background to SDF panels"""

    def draw_arcane_background(self, context):
        """Draw arcane.png background for this panel"""
        try:
            region = context.region
            SDFPanelBackground.draw_background(context, region)
        except Exception as e:
            print(f"SDF Panel Mixin: Error drawing background: {e}")


# Registration
def register():
    """Register SDF panel background system"""
    # Initialize the background system
    SDFPanelBackground.initialize()


def unregister():
    """Unregister SDF panel background system"""
    SDFPanelBackground.cleanup()
