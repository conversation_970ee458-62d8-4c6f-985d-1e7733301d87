import bpy
import os
from bpy.types import Operator
from bpy.props import StringProperty
from bpy_extras.io_utils import ImportHelper


class SDF_OT_LoadDefaultMatcap(Operator):
    """Load default matcap texture"""
    bl_idname = "sdf.load_default_matcap"
    bl_label = "Load Default Matcap"
    bl_description = "Load a default matcap texture"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        try:
            from .shaders import SDFRenderer
            
            # Create default matcap
            if SDFRenderer.create_default_matcap():
                # Update material properties
                scene = context.scene
                if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
                    scene.sdf.material.matcap_image = "SDF_Default_Matcap"
                    scene.sdf.material.use_matcap = True
                    if scene.sdf.material.shading_mode == '0':
                        scene.sdf.material.shading_mode = '1'  # Switch to matcap mode
                
                self.report({'INFO'}, "Default matcap loaded successfully")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to create default matcap")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error loading default matcap: {e}")
            return {'CANCELLED'}


class SDF_OT_LoadMatcapFile(Operator, ImportHelper):
    """Load matcap texture from file"""
    bl_idname = "sdf.load_matcap_file"
    bl_label = "Load Matcap File"
    bl_description = "Load a matcap texture from file"
    bl_options = {'REGISTER', 'UNDO'}
    
    # File browser properties
    filename_ext = ""
    filter_glob: StringProperty(
        default="*.png;*.jpg;*.jpeg;*.tga;*.bmp;*.tiff;*.exr;*.hdr",
        options={'HIDDEN'},
        maxlen=255,
    )
    
    def execute(self, context):
        try:
            from .shaders import SDFRenderer
            
            filepath = self.filepath
            if not os.path.exists(filepath):
                self.report({'ERROR'}, f"File not found: {filepath}")
                return {'CANCELLED'}
            
            # Load the matcap texture
            if SDFRenderer.load_matcap_texture(filepath):
                # Update material properties
                scene = context.scene
                if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
                    # Get the image name from the loaded file
                    image_name = os.path.basename(filepath)
                    scene.sdf.material.matcap_image = image_name
                    scene.sdf.material.use_matcap = True
                    if scene.sdf.material.shading_mode == '0':
                        scene.sdf.material.shading_mode = '1'  # Switch to matcap mode
                
                self.report({'INFO'}, f"Matcap loaded successfully: {os.path.basename(filepath)}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, f"Failed to load matcap: {os.path.basename(filepath)}")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error loading matcap file: {e}")
            return {'CANCELLED'}


class SDF_OT_LoadMatcapFromBlender(Operator):
    """Load matcap from existing Blender image"""
    bl_idname = "sdf.load_matcap_from_blender"
    bl_label = "Load from Blender Image"
    bl_description = "Load matcap from an existing Blender image"
    bl_options = {'REGISTER', 'UNDO'}
    
    image_name: StringProperty(
        name="Image Name",
        description="Name of the image in Blender",
        default=""
    )
    
    def invoke(self, context, event):
        # Get current matcap image name if set
        scene = context.scene
        if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
            self.image_name = scene.sdf.material.matcap_image
        
        return context.window_manager.invoke_props_dialog(self)
    
    def draw(self, context):
        layout = self.layout
        layout.prop_search(self, "image_name", bpy.data, "images")
    
    def execute(self, context):
        try:
            from .shaders import SDFRenderer
            
            if not self.image_name:
                self.report({'ERROR'}, "No image name specified")
                return {'CANCELLED'}
            
            # Load the matcap texture from Blender image
            if SDFRenderer.load_matcap_from_blender_image(self.image_name):
                # Update material properties
                scene = context.scene
                if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
                    scene.sdf.material.matcap_image = self.image_name
                    scene.sdf.material.use_matcap = True
                    if scene.sdf.material.shading_mode == '0':
                        scene.sdf.material.shading_mode = '1'  # Switch to matcap mode
                
                self.report({'INFO'}, f"Matcap loaded from Blender image: {self.image_name}")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, f"Failed to load matcap from Blender image: {self.image_name}")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Error loading matcap from Blender: {e}")
            return {'CANCELLED'}


class SDF_OT_LoadViewportMatcap(Operator):
    """Load matcap from current viewport shading"""
    bl_idname = "sdf.load_viewport_matcap"
    bl_label = "Load from Viewport"
    bl_description = "Load the matcap currently used in the viewport"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            from .shaders import SDFRenderer

            # Load the current viewport matcap
            if SDFRenderer.load_viewport_matcap():
                # Update material properties
                scene = context.scene
                if hasattr(scene, 'sdf') and hasattr(scene.sdf, 'material'):
                    # Get the current studio light name
                    for area in context.screen.areas:
                        if area.type == 'VIEW_3D':
                            space = area.spaces[0]
                            shading = space.shading
                            if shading.type == 'SOLID' and shading.light == 'MATCAP':
                                scene.sdf.material.matcap_studio_light = shading.studio_light
                                scene.sdf.material.use_matcap = True
                                if scene.sdf.material.shading_mode == '0':
                                    scene.sdf.material.shading_mode = '1'  # Switch to matcap mode
                                break

                self.report({'INFO'}, "Viewport matcap loaded successfully")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to load viewport matcap")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Error loading viewport matcap: {e}")
            return {'CANCELLED'}


# List of classes to register
classes = (
    SDF_OT_LoadDefaultMatcap,
    SDF_OT_LoadMatcapFile,
    SDF_OT_LoadMatcapFromBlender,
    SDF_OT_LoadViewportMatcap,
)


def register():
    """Register matcap operators"""
    for cls in classes:
        try:
            bpy.utils.register_class(cls)
        except ValueError as e:
            print(f"Arcane SDF: Class {cls.__name__} already registered: {e}")


def unregister():
    """Unregister matcap operators"""
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except ValueError as e:
            print(f"Arcane SDF: Class {cls.__name__} not registered: {e}")
